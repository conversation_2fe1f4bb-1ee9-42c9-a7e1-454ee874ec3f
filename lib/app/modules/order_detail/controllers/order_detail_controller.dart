import 'dart:async';

import 'package:flutter_sunmi_printer/flutter_sunmi_printer.dart';
import 'package:get/get.dart';
import 'package:guests/app/models/order_detail_model.dart' show OrderDetail;
import 'package:guests/app/providers/box_provider.dart';
import 'package:guests/app/providers/invoice_provider.dart';
import 'package:guests/app/providers/order_provider.dart';
import 'package:guests/app/providers/pref_provider.dart';
import 'package:guests/constants.dart';
import 'package:guests/enums.dart';
import 'package:guests/extension.dart';
import 'package:guests/keys.dart';
import 'package:screenshot/screenshot.dart';

class OrderDetailController extends GetxController with StateMixin<String> {
  // 發票截圖
  final widgetUpdater = Completer();
  final screenshotController = ScreenshotController();
  final OrderProvider orderProvider;
  final InvoiceProvider invoiceProvider;
  final BoxProvider boxProvider;
  PrefProvider get prefProvider => orderProvider.prefProvider;
  String get productName => prefProvider.itemName;
  // order id
  final _id = ''.obs;
  String get id => _id.value;
  // order detail
  final _data = OrderDetail().obs;
  OrderDetail get data => _data.value;
  // order draft
  final _draft = OrderDetail().obs;
  OrderDetail get draft => _draft.value;
  //
  final _invoiceVisible = false.obs;
  bool get invoiceVisible => _invoiceVisible.value;

  OrderDetailController({
    required this.orderProvider,
    required this.invoiceProvider,
    required this.boxProvider,
  });

  @override
  void onInit() {
    super.onInit();
    if (Get.parameters.containsKey(Keys.Id)) {
      _id.value = Get.parameters[Keys.Id] ?? '';
    }
  }

  @override
  void onReady() {
    onRefresh();
  }

  @override
  void onClose() {
    //
  }

  Future<void> onRefresh() async {
    try {
      _data.value = await orderProvider.getOrderDetail(id);
      change('', status: RxStatus.success());
    } catch (e) {
      change('', status: RxStatus.error('$e'));
    }
  }

  void refreshDraft() {
    _draft.refresh();
  }

  Future<num> refundOrder() async {
    final orderId = await orderProvider.refundOrder(id);
    // 字軌
    final invoiceNumber = data.invoiceNumber;
    if (invoiceNumber.isNotEmpty) {
      try {
        await uploadCancel();
      } catch (e) {
        // 作廢發票失敗，加入補上傳佇列
        final box = await boxProvider.getLazyBox(Keys.BoxOrderInvoice);
        final key = '$orderId.${BpscmInvoiceStatus.Cancel.index}';
        await box.put(key, invoiceNumber);
      }
    }
    return orderId;
  }

  Future<String> uploadCancel() async {
    final invoice = data.toInvoice();
    // HACK: 測試上傳發票失敗
    // throw '作廢發票失敗: 字軌(${invoice.invoiceNumber})';
    // 作廢發票
    final ls = await invoiceProvider.editInvoice(
      seller: invoice.sellerIdentifier!,
      invoiceNo: invoice.invoiceNumber!,
      // orderNo: this.cached.value.orderNumber,
      // invoiceDate: invoiceDate.yMdHms,
      invoiceDate: data.createdDateTime!.yMdHms,
      cancelDate: DateTime.now().yMdHms,
      buyer: invoice.buyerIdentifier!,
    );
    // 檢查
    if (ls.isNotEmpty) {
      final element = ls.first;
      if ('OK' == element.status) {
        return element.invoiceNo!;
      }
      throw element.message ?? 'Unknown error';
    }
    throw '上傳作廢失敗: 字軌(${invoice.invoiceNumber})';
  }

  Future<String> uploadInvoice() async {
    final orderDetail = data;
    final invoice = orderDetail.toInvoice();
    // 免稅、應稅、零稅率
    final taxType = prefProvider.taxType;
    final invoiceDate = DateTime.fromMillisecondsSinceEpoch(0)
        .add((invoice.invoiceDate ?? 0).milliseconds);
    final ls = await invoiceProvider.pushInvoice(
      invoiceNo: invoice.invoiceNumber!,
      randomNumber: invoice.randomNumber!,
      seller: invoice.sellerIdentifier!,
      buyer: invoice.buyerIdentifier!,
      orderNo: orderDetail.orderNumber,
      invoiceDate: kDateTimeFormat.format(invoiceDate),
      price: orderDetail.total!,
      itemName: orderDetail.productName,
      taxType: taxType,
    );
    if (ls.isNotEmpty) {
      final element = ls.first;
      if ('OK' == element.status) {
        return element.invoiceNo!;
      } else {
        throw element.message ?? 'Unknown error';
      }
    }
    throw '上傳發票失敗: 字軌(${invoice.invoiceNumber})';
  }

  Future<void> print() async {
    final invoice = data.toSunmiInvoice();
    await SunmiPrinter.printInvoice(invoice);
  }

  // 使用截圖列印
  // Future<void> print() async {
  //   try {
  //     _invoiceVisible.value = true;
  //     // 等待內容變更完成
  //     await widgetUpdater.future;
  //     // 發票截圖
  //     final file = await screenshotController.capture(
  //       delay: 200.milliseconds,
  //       pixelRatio: 1.0,
  //     );
  //     final bytes = await file.readAsBytes();
  //     final uri = Uri.dataFromBytes(bytes);
  //     await SunmiPrinter.image(uri.data.contentText, align: SunmiAlign.left);
  //     await SunmiPrinter.emptyLines(3);
  //   } catch (e) {
  //     rethrow;
  //   } finally {
  //     _invoiceVisible.value = false;
  //   }
  // }
}
