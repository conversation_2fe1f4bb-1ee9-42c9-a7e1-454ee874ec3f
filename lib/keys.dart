class Keys {
  final String value;
  const Keys._internal(this.value);
  // static const Id = 'id';
  static const Tag = 'tag';
  static const Id = 'id';
  static const CouponId = 'coupon_id';
  static const MemberId = 'member_id';
  static const Data = 'data';
  static const Title = 'title';
  static const Points = 'points';

  // 基础字段
  static const Key = 'key';
  static const Val = 'val';
  static const Name = 'name';
  static const Page = 'page';
  static const Text = 'text';
  static const Icon = 'icon';
  static const Limit = 'limit';

  static const BrandId = 'brand_id';
  static const BrandsInfo = 'brands_info';
  static const IsUpdated = 'is_updated';
  static const IsDeleted = 'is_deleted';
  static const IsCreated = 'is_created';
  static const IsLogout = 'is_logout';
  static const IsSorted = 'is_sorted';
  static const IsPrint = 'is_print';
  static const StoreType = 'store_type';
  static const Error = 'error';
  static const PayMethods = 'pay_methods';
  static const Token = 'token';
  static const Setting = 'setting';
  static const SettingPay = 'setting_pay';
  static const SettingPoint = 'setting_point';
  static const LocalSettings = 'local_settings';

  // API 相关
  static const Settings = 'settings';
  static const Pagination = 'pagination';
  static const Message = 'message';
  static const Status = 'status';
  static const CreatedAt = 'created_at';
  static const UpdatedAt = 'updated_at';

  static const Keyword = 'keyword';
  static const Password = 'password';
  static const Temporary = 'temporary';
  static const Code = 'code';
  static const Code1104 = '1104';
  static const Code0100 = '0100';
  static const Code3101 = '3101';
  static const BoxOrder = 'order';
  static const BoxOrderDetail = 'order_detail';
  static const BoxOrderInvoice = 'order_invoice';
  static const BoxAccount = 'account';
  static const LoginUser = 'login_user';
  static const ChannelsInfo = 'channels_info';

  // 用户认证相关
  static const Username = 'username';
  static const RememberMe = 'remember_me';
  static const ClientCode = 'client_code';
  static const ClientInfo = 'client_info';
  static const ChannelCode = 'channel_code';

  // 发票相关
  static const InvoiceSkipped = 'invoice_skipped';
  static const InvoiceEnabled = 'invoice_enabled';
  static const InvoiceTaxId = 'invoice_tax_id';
  static const ItemName = 'item_name';
  static const TaxId = 'tax_id';
  static const TaxType = 'tax_type';
}
